<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - Online Watch Store</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        :root {
            --primary-green: #4caf50;
            --dark-black: #181818;
            --light-bg: #f3f7f6;
            --card-bg: #232323;
            --text-light: #f3f7f6;
            --text-dark: #181818;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            background: linear-gradient(135deg, var(--dark-black) 0%, var(--primary-green) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .signup-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 900px;
            display: flex;
            min-height: 650px;
        }

        .signup-left {
            flex: 1;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .signup-right {
            flex: 1;
            background: linear-gradient(135deg, var(--dark-black), #2a2a2a);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 3rem;
            color: white;
            text-align: center;
        }

        .signup-right h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .signup-right p {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .watch-icon {
            font-size: 4rem;
            margin-bottom: 2rem;
            opacity: 0.8;
            color: var(--primary-green);
        }

        .signup-form h2 {
            color: var(--text-dark);
            font-size: 2rem;
            margin-bottom: 0.5rem;
            font-weight: 700;
        }

        .signup-form .subtitle {
            color: #666;
            margin-bottom: 2rem;
            font-size: 1rem;
        }

        .form-row {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
            flex: 1;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-dark);
            font-weight: 600;
            font-size: 0.9rem;
        }

        .form-group input {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-green);
            background: white;
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
        }

        .form-group i {
            position: absolute;
            left: 1rem;
            top: 2.2rem;
            color: #666;
            font-size: 1.1rem;
        }

        .terms-checkbox {
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
            margin-bottom: 2rem;
            font-size: 0.9rem;
        }

        .terms-checkbox input[type="checkbox"] {
            width: auto;
            margin: 0;
            margin-top: 0.2rem;
        }

        .terms-checkbox a {
            color: var(--primary-green);
            text-decoration: none;
            font-weight: 600;
        }

        .terms-checkbox a:hover {
            text-decoration: underline;
        }

        .signup-btn {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, var(--primary-green), #45a049);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }

        .signup-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
        }

        .divider {
            text-align: center;
            margin: 1.5rem 0;
            position: relative;
            color: #666;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e1e5e9;
        }

        .divider span {
            background: white;
            padding: 0 1rem;
        }

        .login-link {
            text-align: center;
            color: #666;
        }

        .login-link a {
            color: var(--primary-green);
            text-decoration: none;
            font-weight: 600;
        }

        .login-link a:hover {
            text-decoration: underline;
        }

        .back-home {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .back-home:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .signup-container {
                flex-direction: column;
                max-width: 400px;
            }
            
            .signup-right {
                padding: 2rem;
            }
            
            .signup-right h1 {
                font-size: 2rem;
            }
            
            .watch-icon {
                font-size: 3rem;
            }
            
            .signup-left {
                padding: 2rem;
            }
            
            .form-row {
                flex-direction: column;
                gap: 0;
            }
        }

        .success-message, .error-message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: none;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .password-strength {
            font-size: 0.8rem;
            margin-top: 0.5rem;
            padding: 0.5rem;
            border-radius: 6px;
            display: none;
        }

        .password-strength.weak {
            background: #f8d7da;
            color: #721c24;
        }

        .password-strength.medium {
            background: #fff3cd;
            color: #856404;
        }

        .password-strength.strong {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <a href="home.html" class="back-home">
        <i class="fas fa-arrow-left"></i> Back to Home
    </a>

    <div class="signup-container">
        <div class="signup-left">
            <div class="signup-form">
                <h2>Create Account</h2>
                <p class="subtitle">Join our community and discover premium timepieces</p>

                <div id="successMessage" class="success-message"></div>
                <div id="errorMessage" class="error-message"></div>

                <form id="signupForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="firstName">First Name</label>
                            <i class="fas fa-user"></i>
                            <input type="text" id="firstName" name="firstName" placeholder="Enter first name" required>
                        </div>
                        <div class="form-group">
                            <label for="lastName">Last Name</label>
                            <i class="fas fa-user"></i>
                            <input type="text" id="lastName" name="lastName" placeholder="Enter last name" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="email">Email Address</label>
                        <i class="fas fa-envelope"></i>
                        <input type="email" id="email" name="email" placeholder="Enter your email" required>
                    </div>

                    <div class="form-group">
                        <label for="phone">Phone Number</label>
                        <i class="fas fa-phone"></i>
                        <input type="tel" id="phone" name="phone" placeholder="Enter phone number" required>
                    </div>

                    <div class="form-group">
                        <label for="password">Password</label>
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" placeholder="Create password" required>
                        <div id="passwordStrength" class="password-strength"></div>
                    </div>

                    <div class="form-group">
                        <label for="confirmPassword">Confirm Password</label>
                        <i class="fas fa-lock"></i>
                        <input type="password" id="confirmPassword" name="confirmPassword" placeholder="Confirm password" required>
                    </div>

                    <label class="terms-checkbox">
                        <input type="checkbox" id="terms" name="terms" required>
                        I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a>
                    </label>

                    <button type="submit" class="signup-btn">
                        <i class="fas fa-user-plus"></i> Create Account
                    </button>
                </form>

                <div class="divider">
                    <span>or</span>
                </div>

                <div class="login-link">
                    Already have an account? <a href="login.html">Sign In</a>
                </div>
            </div>
        </div>

        <div class="signup-right">
            <div class="watch-icon">
                <i class="fas fa-clock"></i>
            </div>
            <h1>Join Our Family!</h1>
            <p>Create your account to unlock exclusive deals, track your orders, and be the first to know about new arrivals in our premium watch collection.</p>
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:4001/api';

        // Check if user is already logged in
        document.addEventListener('DOMContentLoaded', function() {
            const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
            const tokenExpiry = localStorage.getItem('tokenExpiry');

            // Check if token is still valid
            if (isLoggedIn && tokenExpiry && Date.now() < parseInt(tokenExpiry)) {
                // User is already logged in, redirect to home
                window.location.href = 'home.html';
                return;
            }
        });

        // Password strength checker
        document.getElementById('password').addEventListener('input', function(e) {
            const password = e.target.value;
            const strengthDiv = document.getElementById('passwordStrength');

            if (password.length === 0) {
                strengthDiv.style.display = 'none';
                return;
            }

            const strength = checkPasswordStrength(password);
            strengthDiv.style.display = 'block';
            strengthDiv.className = `password-strength ${strength.class}`;
            strengthDiv.textContent = strength.message;
        });

        // Confirm password validation
        document.getElementById('confirmPassword').addEventListener('input', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = e.target.value;

            if (confirmPassword && password !== confirmPassword) {
                e.target.setCustomValidity('Passwords do not match');
            } else {
                e.target.setCustomValidity('');
            }
        });

        // Form submission
        document.getElementById('signupForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const submitButton = e.target.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;

            // Show loading state
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Account...';
            submitButton.disabled = true;

            const formData = new FormData(e.target);
            const signupData = {
                firstName: formData.get('firstName').trim(),
                lastName: formData.get('lastName').trim(),
                email: formData.get('email').trim(),
                phone: formData.get('phone').trim(),
                password: formData.get('password'),
                confirmPassword: formData.get('confirmPassword')
            };

            // Client-side validation
            if (!signupData.firstName || !signupData.lastName || !signupData.email ||
                !signupData.phone || !signupData.password || !signupData.confirmPassword) {
                showError('All fields are required');
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
                return;
            }

            if (signupData.password !== signupData.confirmPassword) {
                showError('Passwords do not match');
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
                return;
            }

            if (signupData.password.length < 6) {
                showError('Password must be at least 6 characters long');
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(signupData.email)) {
                showError('Please enter a valid email address');
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
                return;
            }

            // Phone validation (basic)
            const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
            if (!phoneRegex.test(signupData.phone.replace(/\s/g, ''))) {
                showError('Please enter a valid phone number');
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
                return;
            }

            try {
                const response = await fetch(`${API_URL}/register`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(signupData)
                });

                const result = await response.json();

                if (response.ok) {
                    showSuccess('Account created successfully! Redirecting to login...');

                    // Clear form
                    e.target.reset();
                    document.getElementById('passwordStrength').style.display = 'none';

                    // Redirect to login page after short delay
                    setTimeout(() => {
                        window.location.href = `login.html?email=${encodeURIComponent(signupData.email)}`;
                    }, 2000);
                } else {
                    showError(result.message || 'Registration failed. Please try again.');
                }
            } catch (error) {
                showError('Connection error. Please check your internet connection.');
                console.error('Signup error:', error);
            } finally {
                // Reset button state
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            }
        });

        function checkPasswordStrength(password) {
            let score = 0;

            if (password.length >= 8) score++;
            if (/[a-z]/.test(password)) score++;
            if (/[A-Z]/.test(password)) score++;
            if (/[0-9]/.test(password)) score++;
            if (/[^A-Za-z0-9]/.test(password)) score++;

            if (score < 3) {
                return { class: 'weak', message: 'Weak password - Add uppercase, numbers, and symbols' };
            } else if (score < 4) {
                return { class: 'medium', message: 'Medium strength - Consider adding more complexity' };
            } else {
                return { class: 'strong', message: 'Strong password!' };
            }
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            const errorDiv = document.getElementById('errorMessage');

            successDiv.textContent = message;
            successDiv.style.display = 'block';
            errorDiv.style.display = 'none';

            setTimeout(() => {
                successDiv.style.display = 'none';
            }, 5000);
        }

        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            const successDiv = document.getElementById('successMessage');

            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            successDiv.style.display = 'none';
        }
    </script>
</body>
</html>
