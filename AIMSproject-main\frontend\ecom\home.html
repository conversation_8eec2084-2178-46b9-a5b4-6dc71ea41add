<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Online Watch Store</title>
    <link rel="stylesheet" href="home.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        :root {
          --primary-green: #4caf50;
          --dark-black: #181818;
          --light-bg: #f3f7f6;
          --card-bg: #232323;
          --text-light: #f3f7f6;
          --text-dark: #181818;
        }
        body {
          background: var(--light-bg);
          color: var(--text-dark);
          font-family: 'Segoe UI', Arial, sans-serif;
        }
        .home {
          background: var(--light-bg);
        }
        .landingpage {
          background: linear-gradient(120deg, var(--primary-green) 60%, var(--dark-black) 100%);
          color: var(--text-light);
        }
        .homecontent h1, .homecontent h4, .homecontent .offer {
          color: var(--text-dark);
        }
        .homecontent h1 span {
          color: var(--primary-green);
        }
        .shopbtn {
          background: var(--primary-green);
          color: var(--text-light);
          border: none;
          border-radius: 5px;
          padding: 10px 32px;
          font-size: 1.1rem;
          font-weight: 700;
          margin-top: 10px;
          cursor: pointer;
          transition: background 0.2s;
        }
        .shopbtn:hover {
          background: var(--dark-black);
          color: var(--primary-green);
        }
        .contactbtn {
          background: var(--dark-black);
          color: var(--primary-green);
          border: 2px solid var(--primary-green);
          border-radius: 5px;
          padding: 10px 32px;
          font-size: 1.1rem;
          font-weight: 700;
          cursor: pointer;
          transition: all 0.3s ease;
        }
        .contactbtn:hover {
          background: var(--primary-green);
          color: var(--dark-black);
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
        }
        .homefeatures .features {
          background: var(--card-bg);
          color: var(--text-light);
        }
        .options .icon i {
          color: var(--primary-green);
        }
        .options .info h4 {
          color: var(--primary-green);
        }
        .options .info span {
          color: var(--text-light);
        }
        .card.featured-card {
          background: var(--card-bg) !important;
          color: var(--text-light);
        }
        .card.featured-card h3 {
          color: var(--text-dark);
          font-weight: 700;
        }
        .card.featured-card p {
          color: var(--text-light);
        }
        .card.featured-card .shopbtn {
          background: var(--primary-green);
          color: var(--text-light);
        }
        .card.featured-card .shopbtn:hover {
          background: var(--text-light);
          color: var(--primary-green);
        }
        .featured-card {
          transition: transform 0.3s cubic-bezier(.4,2,.6,1), box-shadow 0.3s;
        }
        .featured-card:hover {
          transform: translateY(-18px) scale(1.03);
          box-shadow: 0 8px 24px rgba(0,0,0,0.13);
          z-index: 2;
        }

        /* Horizontal Card Styles */
        .horizontal-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
        }

        .horizontal-card .shopbtn:hover {
          background: var(--dark-black) !important;
          color: var(--primary-green) !important;
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
        }

        .horizontal-card button:last-child:hover {
          background: var(--primary-green) !important;
          color: var(--dark-black) !important;
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
        }

        /* Responsive Design for Horizontal Card */
        @media (max-width: 768px) {
          .horizontal-card {
            min-height: 300px !important;
            border-radius: 15px !important;
          }
        }

        @media (max-width: 480px) {
          .horizontal-card {
            min-height: 250px !important;
            border-radius: 10px !important;
          }
        }
        /* Navigation Header Styles */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            z-index: 1000;
            padding: 1rem 0;
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 2px 25px rgba(0,0,0,0.15);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-dark);
            text-decoration: none;
        }

        .nav-logo i {
            color: var(--primary-green);
            font-size: 1.8rem;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            gap: 2rem;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .nav-menu a {
            text-decoration: none;
            color: var(--text-dark);
            font-weight: 500;
            transition: color 0.3s ease;
            position: relative;
        }

        .nav-menu a:hover {
            color: var(--primary-green);
        }

        .nav-menu a::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--primary-green);
            transition: width 0.3s ease;
        }

        .nav-menu a:hover::after {
            width: 100%;
        }

        .nav-auth {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .nav-login {
            background: transparent;
            color: var(--text-dark);
            border: 2px solid var(--primary-green);
            padding: 0.5rem 1.5rem;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-login:hover {
            background: var(--primary-green);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
        }

        .nav-signup {
            background: var(--primary-green);
            color: white;
            border: 2px solid var(--primary-green);
            padding: 0.5rem 1.5rem;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-signup:hover {
            background: var(--dark-black);
            border-color: var(--dark-black);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(24, 24, 24, 0.3);
        }

        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--text-dark);
            cursor: pointer;
        }

        /* User Profile Dropdown */
        .user-profile {
            position: relative;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-green);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .user-avatar:hover {
            background: var(--dark-black);
            transform: scale(1.05);
        }

        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 1rem 0;
            min-width: 200px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .user-dropdown.active {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .user-dropdown a {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            color: var(--text-dark);
            text-decoration: none;
            transition: background 0.3s ease;
        }

        .user-dropdown a:hover {
            background: #f8f9fa;
        }

        .user-dropdown .logout-btn {
            color: #dc3545;
            border-top: 1px solid #e9ecef;
            margin-top: 0.5rem;
            padding-top: 1rem;
        }

        .user-dropdown .logout-btn:hover {
            background: #f8d7da;
        }

        .user-welcome {
            color: var(--text-dark);
            font-weight: 600;
            font-size: 0.9rem;
        }

        /* Mobile Navigation */
        @media (max-width: 768px) {
            .nav-menu {
                position: fixed;
                top: 80px;
                left: -100%;
                width: 100%;
                height: calc(100vh - 80px);
                background: white;
                flex-direction: column;
                justify-content: flex-start;
                align-items: center;
                padding-top: 2rem;
                transition: left 0.3s ease;
                box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            }

            .nav-menu.active {
                left: 0;
            }

            .nav-menu li {
                margin: 1rem 0;
            }

            .nav-auth {
                flex-direction: column;
                gap: 1rem;
                margin-top: 2rem;
            }

            .mobile-menu-toggle {
                display: block;
            }
        }

        /* Adjust main content for fixed navbar */
        .home {
            padding-top: 80px;
        }
    </style>
</head>
<body>
    <!-- Navigation Header -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <a href="home.html" class="nav-logo">
                <i class="fas fa-clock"></i>
                <span>TimeZone</span>
            </a>

            <ul class="nav-menu" id="nav-menu">
                <li><a href="home.html">Home</a></li>
                <li><a href="shop.html">Shop</a></li>
                <li><a href="about.html">About</a></li>
                <li><a href="contact.html">Contact</a></li>
                <li class="nav-auth" id="nav-auth">
                    <!-- Will be populated by JavaScript based on login status -->
                </li>
            </ul>

            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </nav>

    <main class="home">
        <section class="landingpage">
            <div class="homecontainer">
                <div class="homecontent">
                    <h4 class="promotion">Exclusive Watches</h4>
                    <h1 class="fashion">Timeless <span>Elegance</span></h1>
                    <h1 class="collection">Watch Collection</h1>
                    <span class="offer">Save more with coupons & up to 20% off on premium watches</span>
                    <div style="display: flex; gap: 1rem; margin-top: 10px;">
                        <button class="shopbtn" onclick="window.open('shop.html', '_blank')">Shop Watches</button>
                        <button class="contactbtn" onclick="window.open('contact.html', '_blank')">📞 Contact Us</button>
                    </div>
                </div>
                <div class="homeimg">
                    <img id="banner-watch" src="https://themewagon.com/wp-content/uploads/2021/05/Watch.png" alt="Watch Showcase" style="transition: opacity 0.5s;">
                </div>
            </div>
        </section>
        <section class="homefeatures">
            <div class="features">
                <div class="options">
                    <div class="icon">
                        <i class="fa-solid fa-truck"></i>
                    </div>
                    <div class="info">
                        <h4>Free delivery</h4>
                        <span>Worldwide shipping on all watches</span>
                    </div>
                </div>
                <div class="options">
                    <div class="icon">
                        <i class="fa-solid fa-certificate"></i>
                    </div>
                    <div class="info">
                        <h4>Authenticity Guarantee</h4>
                        <span>100% genuine watches</span>
                    </div>
                </div>
                <div class="options">
                    <div class="icon">
                        <i class="fa-solid fa-hand-holding-dollar"></i>
                    </div>
                    <div class="info">
                        <h4>Money Back</h4>
                        <span>30-day return policy</span>
                    </div>
                </div>
                <div class="options">
                    <div class="icon">
                        <i class="fa-solid fa-shield-halved"></i>
                    </div>
                    <div class="info">
                        <h4>Secure Payment</h4>
                        <span>Safe & encrypted checkout</span>
                    </div>
                </div>
                <div class="options">
                    <div class="icon">
                        <i class="fa-solid fa-undo"></i>
                    </div>
                    <div class="info">
                        <h4>Easy Returns</h4>
                        <span>Hassle-free returns</span>
                    </div>
                </div>
            </div>
        </section>
        <section style="padding: 3rem 0; background: linear-gradient(135deg, #f8f9fa, #e9ecef);">
            <div class="container" style="max-width: 1200px; margin: 0 auto; padding: 0 2rem;">
                <div class="horizontal-card" id="edifice-card" style="
                    display: block;
                    background: url('https://www.casio.com/content/casio/locales/in/en/products/watches/edifice/_jcr_content/root/responsivegrid/carousel_1653277841/item_1722246114946.casiocoreimg.jpeg/1722246635049/desktop--edifice-night-time.jpeg') center/cover;
                    border-radius: 20px;
                    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
                    overflow: hidden;
                    min-height: 500px;
                    width: 100%;
                    transition: all 0.8s ease;
                    cursor: pointer;
                    position: relative;
                " onclick="window.open('shop.html', '_blank')">
                    <!-- Premium brand collection badge -->
                    <div style="
                        position: absolute;
                        bottom: 20px;
                        left: 20px;
                        background: rgba(0,0,0,0.8);
                        color: white;
                        padding: 0.7rem 1.2rem;
                        border-radius: 30px;
                        font-size: 0.9rem;
                        font-weight: 700;
                        backdrop-filter: blur(15px);
                        opacity: 0.95;
                        border: 1px solid rgba(76, 175, 80, 0.3);
                        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                    ">
                        ✨ PREMIUM WATCH COLLECTION
                    </div>
                </div>
            </div>
        </section>
    </main>
    <script>
        const images = [
            "https://themewagon.com/wp-content/uploads/2021/05/Watch.png",
            "https://images.pexels.com/photos/2113994/pexels-photo-2113994.jpeg?cs=srgb&dl=pexels-joey-nguy-n-1056657-2113994.jpg&fm=jpg",
            "https://acwo.com/cdn/shop/files/FwIT-Bizz-Website-1st-image_252681f8-2805-4595-a438-96da8aad2079_533x.png?v=1737721607",
            "https://windupwatchshop.com/cdn/shop/files/2024.06.04_Formex_Reef_39.5_mm_Bahama_Blue_Automatic_COSC_-_on_stainless_steel_bracelet_1_header_web.jpg?v=1748646088",
            "https://www.carlington.in/cdn/shop/files/Elite_Men_Hero_Banner_-_web.webp?v=1737611620&width=2800"
        ];
        let idx = 0;
        setInterval(() => {
            idx = (idx + 1) % images.length;
            const img = document.getElementById('banner-watch');
            if (img) {
                img.style.opacity = 0;
                setTimeout(() => {
                    img.src = images[idx];
                    img.style.opacity = 1;
                }, 500);
            }
        }, 3000);

        // Premium watch collection image rotation
        const watchImages = [
            "https://www.casio.com/content/casio/locales/in/en/products/watches/edifice/_jcr_content/root/responsivegrid/carousel_1653277841/item_1722246114946.casiocoreimg.jpeg/1722246635049/desktop--edifice-night-time.jpeg",
            "https://www.casio.com/content/casio/locales/in/en/products/watches/edifice/_jcr_content/root/responsivegrid/carousel_1653277841/item_1722246585967.casiocoreimg.jpeg/1722246703063/desktop--edifice-windflow.jpeg",
            "https://www.omegawatches.com/media/wysiwyg/image-2-3-watchstrapsV2-large.jpg",
            "https://www.tissotwatches.com/dw/image/v2/BKKD_PRD/on/demandware.static/-/Library-Sites-Tissot-SharedLibrary/default/dw76c6ad14/2-PLP/1-PLP-BANNER/DESKTOP/Tis-Digital-Seastar38mm-PLP-Banner-Desktop.jpg",
            "https://www.tissotwatches.com/dw/image/v2/BKKD_PRD/on/demandware.static/-/Library-Sites-Tissot-SharedLibrary/default/dw08f058f1/2-PLP/1-PLP-BANNER/DESKTOP-VISUALS/Tis-Digital-Seastar-40-Powermatic-80-Category-Banner-v3.png"
        ];
        let watchIdx = 0;

        setInterval(() => {
            watchIdx = (watchIdx + 1) % watchImages.length;
            const card = document.getElementById('edifice-card');
            if (card) {
                // Add fade out effect with smooth transition
                card.style.opacity = '0.6';
                card.style.transform = 'scale(0.97)';

                setTimeout(() => {
                    // Change background image
                    card.style.backgroundImage = `url('${watchImages[watchIdx]}')`;

                    // Fade back in with slight scale effect
                    card.style.opacity = '1';
                    card.style.transform = 'scale(1)';
                }, 500);
            }
        }, 4500); // Change every 4.5 seconds for better viewing time

        // Navigation functionality
        document.addEventListener('DOMContentLoaded', function() {
            const navbar = document.getElementById('navbar');
            const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
            const navMenu = document.getElementById('nav-menu');

            // Initialize authentication state
            initializeAuthState();

            // Navbar scroll effect
            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            });

            // Mobile menu toggle
            mobileMenuToggle.addEventListener('click', function() {
                navMenu.classList.toggle('active');
                const icon = this.querySelector('i');
                if (navMenu.classList.contains('active')) {
                    icon.classList.remove('fa-bars');
                    icon.classList.add('fa-times');
                } else {
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-bars');
                }
            });

            // Close mobile menu when clicking on a link
            navMenu.addEventListener('click', function(e) {
                if (e.target.tagName === 'A') {
                    navMenu.classList.remove('active');
                    const icon = mobileMenuToggle.querySelector('i');
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-bars');
                }
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', function(e) {
                if (!navbar.contains(e.target)) {
                    navMenu.classList.remove('active');
                    const icon = mobileMenuToggle.querySelector('i');
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-bars');
                }
            });
        });

        // Authentication state management
        function initializeAuthState() {
            const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
            const user = JSON.parse(localStorage.getItem('user') || '{}');
            const token = localStorage.getItem('token');
            const tokenExpiry = localStorage.getItem('tokenExpiry');

            // Check if token is expired
            if (tokenExpiry && Date.now() > parseInt(tokenExpiry)) {
                logout();
                return;
            }

            const navAuth = document.getElementById('nav-auth');

            if (isLoggedIn && user.firstName && token) {
                // User is logged in - show profile
                showUserProfile(user);
            } else {
                // User is not logged in - show login/signup buttons
                showAuthButtons();
            }
        }

        function showUserProfile(user) {
            const navAuth = document.getElementById('nav-auth');
            const initials = (user.firstName.charAt(0) + user.lastName.charAt(0)).toUpperCase();

            navAuth.innerHTML = `
                <div class="user-profile">
                    <span class="user-welcome">Welcome, ${user.firstName}!</span>
                    <div class="user-avatar" onclick="toggleUserDropdown()">
                        ${initials}
                    </div>
                    <div class="user-dropdown" id="user-dropdown">
                        <a href="#" onclick="viewProfile()">
                            <i class="fas fa-user"></i>
                            My Profile
                        </a>
                        <a href="shop.html">
                            <i class="fas fa-shopping-bag"></i>
                            My Orders
                        </a>
                        <a href="#" onclick="viewCart()">
                            <i class="fas fa-shopping-cart"></i>
                            Cart
                        </a>
                        <a href="#" onclick="logout()" class="logout-btn">
                            <i class="fas fa-sign-out-alt"></i>
                            Logout
                        </a>
                    </div>
                </div>
            `;
        }

        function showAuthButtons() {
            const navAuth = document.getElementById('nav-auth');
            navAuth.innerHTML = `
                <a href="login.html" class="nav-login">
                    <i class="fas fa-sign-in-alt"></i>
                    Login
                </a>
                <a href="signup.html" class="nav-signup">
                    <i class="fas fa-user-plus"></i>
                    Sign Up
                </a>
            `;
        }

        function toggleUserDropdown() {
            const dropdown = document.getElementById('user-dropdown');
            dropdown.classList.toggle('active');
        }

        function viewProfile() {
            alert('Profile page coming soon!');
            toggleUserDropdown();
        }

        function viewCart() {
            window.location.href = 'cart.html';
        }

        async function logout() {
            try {
                const token = localStorage.getItem('token');
                if (token) {
                    // Call logout endpoint
                    await fetch('http://localhost:4001/api/logout', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });
                }
            } catch (error) {
                console.error('Logout error:', error);
            } finally {
                // Clear local storage
                localStorage.removeItem('user');
                localStorage.removeItem('token');
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('tokenExpiry');

                // Refresh the page to update UI
                window.location.reload();
            }
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            const userProfile = document.querySelector('.user-profile');
            const dropdown = document.getElementById('user-dropdown');

            if (userProfile && dropdown && !userProfile.contains(e.target)) {
                dropdown.classList.remove('active');
            }
        });

        // Test backend connection
        async function testBackendConnection() {
            try {
                const response = await fetch('http://localhost:4001/api/allproducts');
                const data = await response.json();
                alert(`Backend Connected! Message: ${data.message}`);
                console.log('Backend response:', data);
            } catch (error) {
                alert(`Backend Connection Failed: ${error.message}`);
                console.error('Backend connection error:', error);
            }
        }
    </script>
    <script src="script.js"></script>
</body>
</html>