const express = require('express');
const router = express.Router();
const User = require('../models/usermodel');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

// JWT Secret (in production, use environment variable)
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-here';


router.post('/register', async(req, res) => {
    try {
        console.log('Registration data received:', req.body);
        const { firstName, lastName, email, phone, password, confirmPassword } = req.body;

        // Validation
        if (!firstName || !lastName || !email || !phone || !password) {
            return res.status(400).json({ message: 'All fields are required' });
        }

        if (password !== confirmPassword) {
            return res.status(400).json({ message: 'Passwords do not match' });
        }

        if (password.length < 6) {
            return res.status(400).json({ message: 'Password must be at least 6 characters long' });
        }

        // Check if user already exists
        const existingUser = await User.findOne({
            $or: [{ email }, { phone }]
        });
        if (existingUser) {
            if (existingUser.email === email) {
                return res.status(400).json({ message: 'Email already registered' });
            }
            if (existingUser.phone === phone) {
                return res.status(400).json({ message: 'Phone number already registered' });
            }
        }

        // Create username from first and last name
        const username = `${firstName.toLowerCase()}.${lastName.toLowerCase()}`;

        // Create a new user
        const newUser = new User({
            username,
            firstName,
            lastName,
            email,
            phone,
            password: bcrypt.hashSync(password, 12), // Hash the password with higher salt rounds
        });

        await newUser.save();

        // Create JWT token
        const token = jwt.sign(
            {
                userId: newUser._id,
                email: newUser.email,
                username: newUser.username
            },
            JWT_SECRET,
            { expiresIn: '7d' }
        );

        // Return user data without password
        const userResponse = {
            id: newUser._id,
            username: newUser.username,
            firstName: newUser.firstName,
            lastName: newUser.lastName,
            email: newUser.email,
            phone: newUser.phone,
            isAdmin: newUser.isAdmin,
            createdAt: newUser.createdAt
        };

        return res.status(201).json({
            message: 'User registered successfully',
            user: userResponse,
            token
        });
    } catch (error) {
        console.error('Error registering user:', error);
        if (error.code === 11000) {
            return res.status(400).json({ message: 'Email or phone already exists' });
        }
        res.status(500).json({ message: 'Internal server error' });
    }
})

router.post('/login', async(req, res) => {
    try {
        console.log('Login attempt:', req.body);
        const { email, password, remember } = req.body;

        // Validation
        if (!email || !password) {
            return res.status(400).json({ message: 'Email and password are required' });
        }

        // Check if user exists
        const user = await User.findOne({ email });
        if (!user) {
            return res.status(400).json({ message: 'Invalid email or password' });
        }

        // Check if user is active
        if (!user.isActive) {
            return res.status(400).json({ message: 'Account is deactivated. Please contact support.' });
        }

        // Check if password is correct
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
            return res.status(400).json({ message: 'Invalid email or password' });
        }

        // Update last login
        user.lastLogin = new Date();
        await user.save();

        // Create JWT token with different expiry based on remember me
        const tokenExpiry = remember ? '30d' : '1d';
        const token = jwt.sign(
            {
                userId: user._id,
                email: user.email,
                username: user.username,
                isAdmin: user.isAdmin
            },
            JWT_SECRET,
            { expiresIn: tokenExpiry }
        );

        // Return user data without password
        const userResponse = {
            id: user._id,
            username: user.username,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
            phone: user.phone,
            isAdmin: user.isAdmin,
            lastLogin: user.lastLogin,
            createdAt: user.createdAt
        };

        return res.status(200).json({
            message: 'Login successful',
            user: userResponse,
            token
        });
    } catch (error) {
        console.error('Error logging in user:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
})

// Middleware to verify JWT token
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ message: 'Access token required' });
    }

    jwt.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ message: 'Invalid or expired token' });
        }
        req.user = user;
        next();
    });
};

// Get user profile
router.get('/profile', authenticateToken, async(req, res) => {
    try {
        const user = await User.findById(req.user.userId).select('-password');
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }
        res.status(200).json({ user });
    } catch (error) {
        console.error('Error fetching user profile:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
});

// Logout (client-side token removal, but we can track it server-side if needed)
router.post('/logout', authenticateToken, async(req, res) => {
    try {
        // In a more complex setup, you might want to blacklist the token
        // For now, we'll just send a success response
        res.status(200).json({ message: 'Logged out successfully' });
    } catch (error) {
        console.error('Error logging out user:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
});

// Verify token endpoint
router.get('/verify', authenticateToken, (req, res) => {
    res.status(200).json({
        message: 'Token is valid',
        user: {
            userId: req.user.userId,
            email: req.user.email,
            username: req.user.username,
            isAdmin: req.user.isAdmin
        }
    });
});

module.exports = router;