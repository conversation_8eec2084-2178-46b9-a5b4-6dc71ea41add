{"name": "@types/webidl-conversions", "version": "7.0.3", "description": "TypeScript definitions for webidl-conversions", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/webidl-conversions", "license": "MIT", "contributors": [{"name": "ExE Boss", "githubUsername": "ExE-Boss", "url": "https://github.com/ExE-Boss"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/webidl-conversions"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "ff1514e10869784e8b7cca9c4099a4213d3f14b48c198b1bf116300df94bf608", "typeScriptVersion": "4.5"}