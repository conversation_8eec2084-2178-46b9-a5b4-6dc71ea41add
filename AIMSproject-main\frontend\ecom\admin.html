<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Add Products</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 25px;
            position: relative;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        optgroup {
            font-weight: bold;
            color: #2c3e50;
            background-color: #f8f9fa;
            padding: 5px 0;
        }
        optgroup option {
            font-weight: normal;
            color: #495057;
            padding: 8px 15px;
            background-color: white;
        }
        select option:hover {
            background-color: #e8f5e8;
        }
        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
            transition: border-color 0.3s ease;
        }
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
        }
        select {
            background-color: white;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 12px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
            cursor: pointer;
        }
        select option {
            padding: 10px;
            font-size: 16px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }
        .quick-add {
            background-color: #007bff;
            margin-bottom: 10px;
        }
        .quick-add:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛠️ Admin Panel - Add Products</h1>
        
        <div id="successMessage" class="success"></div>
        <div id="errorMessage" class="error"></div>

        <button class="quick-add" onclick="addSampleProducts()">🚀 Add 5 Sample Watch Products</button>
        
        <form id="productForm">
            <div class="form-group">
                <label for="productname">Product Name:</label>
                <input type="text" id="productname" name="productname" required>
            </div>

            <div class="form-group">
                <label for="price">Price ($):</label>
                <input type="number" id="price" name="price" step="0.01" required>
            </div>

            <div class="form-group">
                <label for="description">Description:</label>
                <textarea id="description" name="description" required></textarea>
            </div>

            <div class="form-group">
                <label for="category">📂 Category:</label>
                <select id="category" name="category" required>
                    <option value="" disabled selected>🎯 Choose a Category</option>
                    <optgroup label="🏆 Premium Collection">
                        <option value="luxury">💎 Luxury Watches</option>
                        <option value="premium">⭐ Premium Collection</option>
                    </optgroup>
                    <optgroup label="🎮 Modern & Tech">
                        <option value="smartwatch">📱 Smart Watches</option>
                        <option value="digital">🔢 Digital Watches</option>
                    </optgroup>
                    <optgroup label="🏃 Active Lifestyle">
                        <option value="sports">⚽ Sports Watches</option>
                        <option value="fitness">💪 Fitness Trackers</option>
                        <option value="outdoor">🏔️ Outdoor Adventure</option>
                    </optgroup>
                    <optgroup label="👔 Classic & Elegant">
                        <option value="classic">🎩 Classic Timepieces</option>
                        <option value="dress">👔 Dress Watches</option>
                        <option value="vintage">🕰️ Vintage Style</option>
                    </optgroup>
                    <optgroup label="👨‍👩‍👧‍👦 Everyday Wear">
                        <option value="casual">👕 Casual Watches</option>
                        <option value="fashion">✨ Fashion Watches</option>
                        <option value="unisex">👫 Unisex Collection</option>
                    </optgroup>
                </select>
            </div>

            <div class="form-group">
                <label for="image">Image URL:</label>
                <input type="url" id="image" name="image" placeholder="https://example.com/image.jpg">
            </div>

            <div class="form-group">
                <label for="rating">Rating (1-5):</label>
                <input type="number" id="rating" name="rating" min="1" max="5" step="0.1" value="4.0">
            </div>

            <div class="form-group">
                <label for="stock">Stock Quantity:</label>
                <input type="number" id="stock" name="stock" min="0" required>
            </div>

            <button type="submit">➕ Add Product</button>
        </form>
    </div>

    <script>
        const API_URL = 'http://localhost:4001/api';

        // Add single product
        document.getElementById('productForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const productData = Object.fromEntries(formData.entries());
            
            // Convert numeric fields
            productData.price = parseFloat(productData.price);
            productData.rating = parseFloat(productData.rating);
            productData.stock = parseInt(productData.stock);

            try {
                const response = await fetch(`${API_URL}/products`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(productData)
                });

                const result = await response.json();

                if (response.ok) {
                    showSuccess(`Product "${productData.productname}" added successfully!`);
                    e.target.reset();
                } else {
                    showError(`Failed to add product: ${result.message}`);
                }
            } catch (error) {
                showError(`Error: ${error.message}`);
            }
        });

        // Add sample products
        async function addSampleProducts() {
            const sampleProducts = [
                {
                    productname: "Rolex Submariner",
                    price: 8999.99,
                    description: "Luxury diving watch with ceramic bezel and automatic movement",
                    category: "luxury",
                    image: "https://images.unsplash.com/photo-**********-81dfa63595aa?w=400",
                    rating: 4.9,
                    stock: 5
                },
                {
                    productname: "Apple Watch Series 9",
                    price: 399.99,
                    description: "Smart watch with health monitoring and GPS",
                    category: "smartwatch",
                    image: "https://images.unsplash.com/photo-**********-1dfe5d97d256?w=400",
                    rating: 4.7,
                    stock: 25
                },
                {
                    productname: "Casio Enticer MTP-VT01D",
                    price: 89.99,
                    description: "Classic analog watch with date display and stainless steel case. Perfect for everyday wear with elegant design.",
                    category: "classic",
                    image: "https://rukminim2.flixcart.com/image/850/1000/xif0q/watch/y/t/k/-original-imagqcq2kkcqhtfp.jpeg?q=90&crop=false",
                    rating: 4.4,
                    stock: 20
                },
                {
                    productname: "Casio G-Shock",
                    price: 149.99,
                    description: "Durable sports watch with shock resistance",
                    category: "sports",
                    image: "https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400",
                    rating: 4.5,
                    stock: 15
                },
                {
                    productname: "Omega Speedmaster",
                    price: 5999.99,
                    description: "Professional chronograph watch, moon landing heritage",
                    category: "luxury",
                    image: "https://images.unsplash.com/photo-1594534475808-b18fc33b045e?w=400",
                    rating: 4.8,
                    stock: 8
                },
                {
                    productname: "Seiko Automatic",
                    price: 299.99,
                    description: "Classic automatic watch with leather strap",
                    category: "classic",
                    image: "https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=400",
                    rating: 4.4,
                    stock: 20
                },
                {
                    productname: "Casio D011 Digital Watch",
                    price: 45.99,
                    description: "Classic Casio digital watch with retro design, alarm, stopwatch, and water resistance. Perfect for everyday wear with vintage appeal.",
                    category: "digital",
                    image: "https://www.casio.com/content/dam/casio/product-info/locales/in/en/timepiece/product/watch/A/A1/A15/A158WA-1/assets/A158WA-1.png.transform/main-visual-pc/image.png",
                    rating: 4.3,
                    stock: 30
                }
            ];

            let successCount = 0;
            let errorCount = 0;

            for (const product of sampleProducts) {
                try {
                    const response = await fetch(`${API_URL}/products`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(product)
                    });

                    if (response.ok) {
                        successCount++;
                    } else {
                        errorCount++;
                    }
                    
                    // Small delay between requests
                    await new Promise(resolve => setTimeout(resolve, 200));
                } catch (error) {
                    errorCount++;
                }
            }

            if (successCount > 0) {
                showSuccess(`Successfully added ${successCount} products!`);
            }
            if (errorCount > 0) {
                showError(`Failed to add ${errorCount} products.`);
            }
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            document.getElementById('errorMessage').style.display = 'none';
            
            setTimeout(() => {
                successDiv.style.display = 'none';
            }, 5000);
        }

        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            document.getElementById('successMessage').style.display = 'none';
        }
    </script>
</body>
</html>
