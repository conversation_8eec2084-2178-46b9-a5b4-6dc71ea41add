{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.1", "nodemon": "^3.1.10"}}